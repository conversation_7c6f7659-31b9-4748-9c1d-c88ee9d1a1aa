#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment一键续杯工具 v1.0.0
作者：多线程吉他
邮箱：<EMAIL>

功能：
1. 一键续杯 - 快速重置环境
2. 环境重置 - 清理VSCode配置、扩展状态和缓存数据
3. 深度清理 - 删除所有Augment相关数据和注册表项
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import os
import sys
import shutil
import winreg
import threading
import time
import subprocess
from pathlib import Path
import json
import ctypes
import uuid
import hashlib

def is_admin():
    """检查是否具有管理员权限"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False



class AugmentCleanerTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment一键续杯工具 v1.0.0")
        self.root.geometry("800x600")
        self.root.resizable(False, False)

        # 初始化变量
        self.progress_var = tk.DoubleVar()
        self.log_text = ""

        # 设置图标和样式
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = tk.Label(self.root, text="Augment一键续杯工具",
                              font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=10)

        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)

        # 三个主要按钮
        self.one_click_btn = tk.Button(button_frame, text="一键续杯",
                                      font=("Microsoft YaHei", 12),
                                      bg="#4CAF50", fg="white",
                                      width=12, height=2,
                                      command=self.one_click_renew)
        self.one_click_btn.grid(row=0, column=0, padx=10)

        self.env_reset_btn = tk.Button(button_frame, text="环境重置",
                                      font=("Microsoft YaHei", 12),
                                      bg="#2196F3", fg="white",
                                      width=12, height=2,
                                      command=self.environment_reset)
        self.env_reset_btn.grid(row=0, column=1, padx=10)

        self.deep_clean_btn = tk.Button(button_frame, text="深度清理",
                                       font=("Microsoft YaHei", 12),
                                       bg="#FF9800", fg="white",
                                       width=12, height=2,
                                       command=self.deep_clean)
        self.deep_clean_btn.grid(row=0, column=2, padx=10)

        # 说明文本
        info_text = ("环境重置将清理VSCode配置、扩展状态和缓存数据，有助于解决Augment插件问题。")
        info_label = tk.Label(self.root, text=info_text,
                             font=("Microsoft YaHei", 10),
                             wraplength=700, justify="left")
        info_label.pack(pady=(10, 20))

        # 机器码信息框架
        machine_frame = tk.LabelFrame(self.root, text="机器码信息",
                                     font=("Microsoft YaHei", 10))
        machine_frame.pack(fill="x", padx=20, pady=(0, 10))

        # 机器码显示
        machine_info_frame = tk.Frame(machine_frame)
        machine_info_frame.pack(fill="x", padx=10, pady=10)

        # 获取机器码信息
        machine_info = self.get_machine_info()

        tk.Label(machine_info_frame, text="CPU序列号:",
                font=("Microsoft YaHei", 9), anchor="w").grid(row=0, column=0, sticky="w", padx=(0, 10))
        tk.Label(machine_info_frame, text=machine_info['cpu_id'],
                font=("Consolas", 9), fg="blue").grid(row=0, column=1, sticky="w")

        tk.Label(machine_info_frame, text="主板序列号:",
                font=("Microsoft YaHei", 9), anchor="w").grid(row=1, column=0, sticky="w", padx=(0, 10))
        tk.Label(machine_info_frame, text=machine_info['motherboard_id'],
                font=("Consolas", 9), fg="blue").grid(row=1, column=1, sticky="w")

        tk.Label(machine_info_frame, text="硬盘序列号:",
                font=("Microsoft YaHei", 9), anchor="w").grid(row=2, column=0, sticky="w", padx=(0, 10))
        tk.Label(machine_info_frame, text=machine_info['disk_id'],
                font=("Consolas", 9), fg="blue").grid(row=2, column=1, sticky="w")

        tk.Label(machine_info_frame, text="MAC地址:",
                font=("Microsoft YaHei", 9), anchor="w").grid(row=3, column=0, sticky="w", padx=(0, 10))
        tk.Label(machine_info_frame, text=machine_info['mac_address'],
                font=("Consolas", 9), fg="blue").grid(row=3, column=1, sticky="w")

        # 日志显示区域
        log_frame = tk.LabelFrame(self.root, text="清理文件路径",
                                 font=("Microsoft YaHei", 10))
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)

        self.log_display = scrolledtext.ScrolledText(log_frame,
                                                    font=("Consolas", 9),
                                                    height=12)
        self.log_display.pack(fill="both", expand=True, padx=5, pady=5)

        # 进度条
        self.progress_bar = ttk.Progressbar(self.root,
                                           variable=self.progress_var,
                                           maximum=100)
        self.progress_bar.pack(fill="x", padx=20, pady=5)

        # 底部按钮
        bottom_frame = tk.Frame(self.root)
        bottom_frame.pack(fill="x", pady=10)

        self.start_env_btn = tk.Button(bottom_frame, text="开始环境重置",
                                      font=("Microsoft YaHei", 10),
                                      bg="#2196F3", fg="white",
                                      width=15,
                                      command=self.start_environment_reset)
        self.start_env_btn.pack(side="left", padx=(20, 10))

        self.clear_log_btn = tk.Button(bottom_frame, text="清空日志",
                                      font=("Microsoft YaHei", 10),
                                      bg="#9E9E9E", fg="white",
                                      width=15,
                                      command=self.clear_log)
        self.clear_log_btn.pack(side="left", padx=10)

        self.save_log_btn = tk.Button(bottom_frame, text="保存日志",
                                     font=("Microsoft YaHei", 10),
                                     bg="#4CAF50", fg="white",
                                     width=15,
                                     command=self.save_log)
        self.save_log_btn.pack(side="right", padx=(10, 20))

        # 底部信息
        bottom_info = tk.Label(self.root,
                              text="作者：多线程吉他 | 邮箱：<EMAIL> | 版本：1.0.0",
                              font=("Microsoft YaHei", 8),
                              fg="gray")
        bottom_info.pack(side="bottom", pady=5)

    def get_machine_info(self):
        """获取机器码相关信息"""
        machine_info = {
            'cpu_id': 'N/A',
            'motherboard_id': 'N/A',
            'disk_id': 'N/A',
            'mac_address': 'N/A'
        }

        try:
            # 获取CPU序列号 - 使用多种方法
            try:
                # 方法1: 使用wmic获取ProcessorId
                result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId', '/value'],
                                      capture_output=True, text=True, timeout=15, shell=True)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'ProcessorId=' in line:
                            cpu_id = line.split('=')[1].strip()
                            if cpu_id and cpu_id != '':
                                machine_info['cpu_id'] = cpu_id
                                break

                # 如果第一种方法失败，尝试第二种方法
                if machine_info['cpu_id'] == 'N/A':
                    result = subprocess.run(['wmic', 'cpu', 'get', 'ProcessorId'],
                                          capture_output=True, text=True, timeout=15, shell=True)
                    if result.returncode == 0:
                        lines = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                        if len(lines) > 1:
                            cpu_id = lines[1].strip()
                            if cpu_id and cpu_id != 'ProcessorId':
                                machine_info['cpu_id'] = cpu_id
            except Exception as e:
                print(f"获取CPU序列号失败: {e}")

            # 获取主板序列号
            try:
                # 方法1: 获取主板序列号
                result = subprocess.run(['wmic', 'baseboard', 'get', 'SerialNumber', '/value'],
                                      capture_output=True, text=True, timeout=15, shell=True)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'SerialNumber=' in line:
                            board_id = line.split('=')[1].strip()
                            if board_id and board_id not in ['', 'To be filled by O.E.M.', 'Default string']:
                                machine_info['motherboard_id'] = board_id
                                break

                # 如果主板序列号无效，尝试获取主板产品名称
                if machine_info['motherboard_id'] == 'N/A':
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'Product', '/value'],
                                          capture_output=True, text=True, timeout=15, shell=True)
                    if result.returncode == 0:
                        for line in result.stdout.split('\n'):
                            if 'Product=' in line:
                                product = line.split('=')[1].strip()
                                if product and product not in ['', 'To be filled by O.E.M.', 'Default string']:
                                    machine_info['motherboard_id'] = f"Product: {product}"
                                    break
            except Exception as e:
                print(f"获取主板信息失败: {e}")

            # 获取硬盘序列号
            try:
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'SerialNumber', '/value'],
                                      capture_output=True, text=True, timeout=15, shell=True)
                if result.returncode == 0:
                    serial_numbers = []
                    for line in result.stdout.split('\n'):
                        if 'SerialNumber=' in line:
                            disk_id = line.split('=')[1].strip()
                            if disk_id and disk_id != '':
                                serial_numbers.append(disk_id)

                    if serial_numbers:
                        # 如果有多个硬盘，显示第一个
                        machine_info['disk_id'] = serial_numbers[0]
                        if len(serial_numbers) > 1:
                            machine_info['disk_id'] += f" (+{len(serial_numbers)-1}个)"
            except Exception as e:
                print(f"获取硬盘序列号失败: {e}")

            # 获取MAC地址
            try:
                # 方法1: 使用getmac命令
                result = subprocess.run(['getmac', '/fo', 'csv', '/v'],
                                      capture_output=True, text=True, timeout=15, shell=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    if len(lines) > 1:
                        # 查找第一个有效的MAC地址
                        for line in lines[1:]:
                            if ',' in line:
                                parts = line.split(',')
                                if len(parts) > 0:
                                    mac = parts[0].strip('"').strip()
                                    if mac and mac != 'N/A' and '-' in mac:
                                        machine_info['mac_address'] = mac
                                        break

                # 方法2: 如果getmac失败，使用wmic
                if machine_info['mac_address'] == 'N/A':
                    result = subprocess.run(['wmic', 'path', 'Win32_NetworkAdapter', 'where',
                                           'NetConnectionStatus=2', 'get', 'MACAddress', '/value'],
                                          capture_output=True, text=True, timeout=15, shell=True)
                    if result.returncode == 0:
                        for line in result.stdout.split('\n'):
                            if 'MACAddress=' in line:
                                mac = line.split('=')[1].strip()
                                if mac and mac != '' and ':' in mac:
                                    machine_info['mac_address'] = mac
                                    break
            except Exception as e:
                print(f"获取MAC地址失败: {e}")

        except Exception as e:
            print(f"获取机器信息时出错: {str(e)}")

        # 如果某些信息仍然是N/A，尝试备用方法
        if machine_info['cpu_id'] == 'N/A':
            machine_info['cpu_id'] = self.get_cpu_id_alternative()

        if machine_info['motherboard_id'] == 'N/A':
            machine_info['motherboard_id'] = self.get_motherboard_id_alternative()

        if machine_info['disk_id'] == 'N/A':
            machine_info['disk_id'] = self.get_disk_id_alternative()

        if machine_info['mac_address'] == 'N/A':
            machine_info['mac_address'] = self.get_mac_address_alternative()

        return machine_info

    def get_cpu_id_alternative(self):
        """备用方法获取CPU ID"""
        try:
            # 尝试从注册表获取CPU信息
            try:
                import winreg
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"HARDWARE\DESCRIPTION\System\CentralProcessor\0")
                cpu_name, _ = winreg.QueryValueEx(key, "ProcessorNameString")
                winreg.CloseKey(key)
                if cpu_name:
                    # 生成基于CPU名称的唯一标识
                    cpu_hash = hashlib.md5(cpu_name.encode()).hexdigest()[:16].upper()
                    return f"CPU-{cpu_hash}"
            except:
                pass

            # 使用机器GUID作为备用
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                   r"SOFTWARE\Microsoft\Cryptography")
                machine_guid, _ = winreg.QueryValueEx(key, "MachineGuid")
                winreg.CloseKey(key)
                if machine_guid:
                    return machine_guid[:16].upper()
            except:
                pass

        except:
            pass
        return "N/A"

    def get_motherboard_id_alternative(self):
        """备用方法获取主板ID"""
        try:
            # 尝试获取BIOS序列号
            result = subprocess.run(['wmic', 'bios', 'get', 'SerialNumber', '/value'],
                                  capture_output=True, text=True, timeout=10, shell=True)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        bios_serial = line.split('=')[1].strip()
                        if bios_serial and bios_serial not in ['', 'To be filled by O.E.M.', 'Default string']:
                            return f"BIOS: {bios_serial}"

            # 尝试获取系统UUID
            result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID', '/value'],
                                  capture_output=True, text=True, timeout=10, shell=True)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'UUID=' in line:
                        system_uuid = line.split('=')[1].strip()
                        if system_uuid and system_uuid != '':
                            return f"UUID: {system_uuid}"
        except:
            pass
        return "N/A"

    def get_disk_id_alternative(self):
        """备用方法获取硬盘ID"""
        try:
            # 尝试获取物理驱动器信息
            result = subprocess.run(['wmic', 'diskdrive', 'get', 'Model,Size', '/value'],
                                  capture_output=True, text=True, timeout=10, shell=True)
            if result.returncode == 0:
                model = ""
                size = ""
                for line in result.stdout.split('\n'):
                    if 'Model=' in line:
                        model = line.split('=')[1].strip()
                    elif 'Size=' in line:
                        size = line.split('=')[1].strip()
                        if size:
                            size = f"{int(size)//1000000000}GB"

                if model:
                    return f"{model} ({size})" if size else model
        except:
            pass
        return "N/A"

    def get_mac_address_alternative(self):
        """备用方法获取MAC地址"""
        try:
            # 使用uuid.getnode()获取MAC地址
            mac_int = uuid.getnode()
            mac_hex = f"{mac_int:012x}"
            mac_formatted = "-".join([mac_hex[i:i+2] for i in range(0, 12, 2)]).upper()
            return mac_formatted
        except:
            pass
        return "N/A"

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("[%H:%M:%S]")
        log_entry = f"{timestamp} {message}\n"
        self.log_display.insert(tk.END, log_entry)
        self.log_display.see(tk.END)
        self.root.update()
        
    def clear_log(self):
        """清空日志"""
        self.log_display.delete(1.0, tk.END)
        self.progress_var.set(0)
        
    def save_log(self):
        """保存日志到文件"""
        try:
            log_content = self.log_display.get(1.0, tk.END)
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"augment_clean_log_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(log_content)
            messagebox.showinfo("保存成功", f"日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("保存失败", f"保存日志时出错: {str(e)}")

    def get_vscode_paths(self):
        """获取VSCode相关路径"""
        user_profile = os.environ.get('USERPROFILE', '')
        appdata = os.environ.get('APPDATA', '')
        localappdata = os.environ.get('LOCALAPPDATA', '')

        paths = {
            'vscode_config': os.path.join(appdata, 'Code'),
            'vscode_extensions': os.path.join(user_profile, '.vscode', 'extensions'),
            'vscode_user_data': os.path.join(appdata, 'Code', 'User'),
            'vscode_logs': os.path.join(appdata, 'Code', 'logs'),
            'vscode_cache': os.path.join(appdata, 'Code', 'CachedData'),
            'vscode_workspace': os.path.join(appdata, 'Code', 'User', 'workspaceStorage'),
            'roaming_code': os.path.join(appdata, 'Code'),
            'local_code': os.path.join(localappdata, 'Programs', 'Microsoft VS Code'),
            'global_storage': os.path.join(appdata, 'Code', 'User', 'globalStorage')
        }

        return paths

    def get_augment_registry_keys(self):
        """获取需要清理的注册表项"""
        registry_keys = [
            (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\VSCode"),
            (winreg.HKEY_CURRENT_USER, r"Software\Classes\vscode"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\{EA457B21-F73E-494C-ACAB-524FDE069978}_is1"),
            (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Run"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
        ]
        return registry_keys

    def safe_remove_directory(self, path):
        """安全删除目录"""
        try:
            if os.path.exists(path):
                # 先尝试修改权限
                os.chmod(path, 0o777)
                shutil.rmtree(path, ignore_errors=True)
                self.log_message(f"✓ 已删除目录: {path}")
                return True
            else:
                self.log_message(f"⚠ 目录不存在: {path}")
                return False
        except Exception as e:
            self.log_message(f"✗ 删除目录失败: {path} - {str(e)}")
            return False

    def safe_remove_file(self, path):
        """安全删除文件"""
        try:
            if os.path.exists(path):
                os.chmod(path, 0o777)
                os.remove(path)
                self.log_message(f"✓ 已删除文件: {path}")
                return True
            else:
                self.log_message(f"⚠ 文件不存在: {path}")
                return False
        except Exception as e:
            self.log_message(f"✗ 删除文件失败: {path} - {str(e)}")
            return False

    def clean_registry_key(self, hkey, key_path, value_name=None):
        """清理注册表项"""
        try:
            if value_name:
                # 删除特定值
                with winreg.OpenKey(hkey, key_path, 0, winreg.KEY_SET_VALUE) as key:
                    winreg.DeleteValue(key, value_name)
                    self.log_message(f"✓ 已删除注册表值: {key_path}\\{value_name}")
            else:
                # 删除整个键
                winreg.DeleteKey(hkey, key_path)
                self.log_message(f"✓ 已删除注册表键: {key_path}")
            return True
        except FileNotFoundError:
            self.log_message(f"⚠ 注册表项不存在: {key_path}")
            return False
        except Exception as e:
            self.log_message(f"✗ 清理注册表失败: {key_path} - {str(e)}")
            return False

    def one_click_renew(self):
        """一键续杯功能"""
        result = messagebox.askyesno("确认操作",
                                   "一键续杯将执行完整的环境清理，包括VSCode配置和注册表清理。\n\n确定要继续吗？")
        if result:
            self.clear_log()
            self.log_message("开始执行一键续杯...")
            threading.Thread(target=self._one_click_renew_worker, daemon=True).start()

    def _one_click_renew_worker(self):
        """一键续杯工作线程"""
        try:
            self.progress_var.set(0)

            # 执行环境重置
            self.log_message("正在执行环境重置...")
            self._environment_reset_worker()

            self.progress_var.set(50)

            # 执行深度清理
            self.log_message("正在执行深度清理...")
            self._deep_clean_worker()

            self.progress_var.set(100)
            self.log_message("一键续杯完成！")
            messagebox.showinfo("完成", "一键续杯操作已完成！")

        except Exception as e:
            self.log_message(f"一键续杯过程中出错: {str(e)}")
            messagebox.showerror("错误", f"一键续杯过程中出错: {str(e)}")

    def environment_reset(self):
        """环境重置功能"""
        result = messagebox.askyesno("确认操作",
                                   "环境重置将清理VSCode配置、扩展状态和缓存数据。\n\n确定要继续吗？")
        if result:
            self.clear_log()
            self.log_message("开始环境重置...")
            threading.Thread(target=self._environment_reset_worker, daemon=True).start()

    def start_environment_reset(self):
        """开始环境重置（底部按钮）"""
        self.environment_reset()

    def _environment_reset_worker(self):
        """环境重置工作线程"""
        try:
            self.progress_var.set(0)
            paths = self.get_vscode_paths()
            total_items = len(paths)

            self.log_message("正在清理VSCode配置...")

            for i, (name, path) in enumerate(paths.items()):
                self.log_message(f"正在清理 {name}...")
                if os.path.isdir(path):
                    self.safe_remove_directory(path)
                elif os.path.isfile(path):
                    self.safe_remove_file(path)

                progress = (i + 1) / total_items * 80  # 80% for file cleanup
                self.progress_var.set(progress)
                time.sleep(0.1)

            # 清理特定的Augment相关文件
            self.log_message("正在清理Augment相关文件...")
            augment_paths = [
                os.path.join(os.environ.get('APPDATA', ''), 'Code', 'User', 'globalStorage', 'augmentcode.augment'),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'augment'),
                os.path.join(os.environ.get('USERPROFILE', ''), '.augment'),
            ]

            for path in augment_paths:
                if os.path.exists(path):
                    if os.path.isdir(path):
                        self.safe_remove_directory(path)
                    else:
                        self.safe_remove_file(path)

            self.progress_var.set(100)
            self.log_message("环境重置完成！")
            messagebox.showinfo("完成", "环境重置操作已完成！")

        except Exception as e:
            self.log_message(f"环境重置过程中出错: {str(e)}")
            messagebox.showerror("错误", f"环境重置过程中出错: {str(e)}")

    def deep_clean(self):
        """深度清理功能"""
        result = messagebox.askyesno("确认操作",
                                   "深度清理将删除所有Augment相关数据和注册表项，此操作不可撤销。\n\n确定要继续吗？")
        if result:
            self.clear_log()
            self.log_message("开始深度清理...")
            threading.Thread(target=self._deep_clean_worker, daemon=True).start()

    def _deep_clean_worker(self):
        """深度清理工作线程"""
        try:
            self.progress_var.set(0)

            # 先执行环境重置
            self.log_message("正在执行环境重置...")
            self._environment_reset_worker()

            self.progress_var.set(50)

            # 清理注册表
            self.log_message("正在清理注册表...")
            registry_keys = self.get_augment_registry_keys()

            for hkey, key_path in registry_keys:
                self.clean_registry_key(hkey, key_path)

            # 清理系统临时文件
            self.log_message("正在清理系统临时文件...")
            temp_paths = [
                os.environ.get('TEMP', ''),
                os.environ.get('TMP', ''),
                os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Temp'),
            ]

            for temp_path in temp_paths:
                if os.path.exists(temp_path):
                    for item in os.listdir(temp_path):
                        if 'vscode' in item.lower() or 'augment' in item.lower():
                            item_path = os.path.join(temp_path, item)
                            if os.path.isdir(item_path):
                                self.safe_remove_directory(item_path)
                            else:
                                self.safe_remove_file(item_path)

            self.progress_var.set(100)
            self.log_message("深度清理完成！")
            messagebox.showinfo("完成", "深度清理操作已完成！")

        except Exception as e:
            self.log_message(f"深度清理过程中出错: {str(e)}")
            messagebox.showerror("错误", f"深度清理过程中出错: {str(e)}")

    def run(self):
        """运行应用程序"""
        # 显示管理员权限状态
        if is_admin():
            self.log_message("✓ 已获取管理员权限")
        else:
            self.log_message("⚠ 当前未以管理员身份运行")

        self.log_message("Augment一键续杯工具已启动")
        self.log_message("请选择需要执行的操作：")
        self.log_message("- 一键续杯：执行完整的环境清理")
        self.log_message("- 环境重置：清理VSCode配置和缓存")
        self.log_message("- 深度清理：删除所有相关数据和注册表项")
        self.log_message("-" * 50)

        self.root.mainloop()

def main():
    """主函数"""
    try:
        # 检查并强制请求管理员权限
        if not is_admin():
            print("正在请求管理员权限...")
            try:
                # 以管理员权限重新启动程序
                ctypes.windll.shell32.ShellExecuteW(
                    None, "runas", sys.executable, " ".join(sys.argv), None, 1
                )
                # 退出当前进程
                sys.exit(0)
            except Exception as e:
                print(f"无法获取管理员权限: {str(e)}")
                print("程序需要管理员权限才能正常工作")
                input("按回车键退出...")
                sys.exit(1)

        print("✓ 已获取管理员权限")

        # 设置DPI感知
        try:
            ctypes.windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

        # 创建并运行应用程序
        app = AugmentCleanerTool()
        app.run()

    except Exception as e:
        try:
            messagebox.showerror("启动错误", f"程序启动失败: {str(e)}")
        except:
            print(f"程序启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
